import csv
import os
from datetime import datetime
from typing import List, Dict, <PERSON><PERSON>
import numpy as np

class AttendanceTracker:
    """
    A class to track detected objects across frames and maintain an attendance log.
    Objects detected for a minimum number of frames are recorded with a name and timestamp.
    """

    def __init__(self, min_frames: int = 30, csv_path: str = "attendance_log.csv"):
        """
        Initialize the AttendanceTracker.

        Args:
            min_frames (int): Minimum number of consecutive frames an object must appear in to be logged.
            csv_path (str): Path to the CSV file for storing attendance records.
        """
        self.min_frames = min_frames
        self.csv_path = csv_path
        self.tracked_objects: Dict[str, Dict] = {}  # Tracks objects by ID: {id: {class_name, count, last_seen}}
        self.attendance_log: List[Dict] = []  # Stores final attendance records: [{name, timestamp}]
        self.logged_objects: set = set()  # Track which objects have already been logged to avoid duplicates
        self._initialize_csv()

    def _initialize_csv(self):
        """Initialize the CSV file with headers if it doesn't exist."""
        if not os.path.exists(self.csv_path):
            with open(self.csv_path, 'w', newline='') as csvfile:
                writer = csv.DictWriter(csvfile, fieldnames=['name', 'timestamp'])
                writer.writeheader()

    def track_objects(self, detections: List[Dict], session_id: str) -> List[Dict]:
        """
        Track objects in a frame and update the attendance log.

        Args:
            detections: List of detections, each with 'class_name', 'confidence', 'box' (x1, y1, x2, y2).
            session_id: Unique identifier for the client session.

        Returns:
            List of current attendance records.
        """
        current_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        current_frame_objects = set()

        for detection in detections:
            class_name = detection['class_name']
            box = detection['box']
            # Use center point and round to nearest 100 pixels for more stable tracking
            x_center = (box[0] + box[2]) // 2
            y_center = (box[1] + box[3]) // 2
            x_region = (x_center // 100) * 100
            y_region = (y_center // 100) * 100
            obj_id = f"{session_id}_{class_name}_{x_region}_{y_region}"

            current_frame_objects.add(obj_id)

            if obj_id in self.tracked_objects:
                self.tracked_objects[obj_id]['count'] += 1
                self.tracked_objects[obj_id]['last_seen'] = current_time
                print(f"[Debug] Object {class_name} count: {self.tracked_objects[obj_id]['count']}")
            else:
                self.tracked_objects[obj_id] = {
                    'class_name': class_name,
                    'count': 1,
                    'last_seen': current_time
                }
                print(f"[Debug] New object tracked: {class_name}")

            # Log to attendance if object persists for min_frames and hasn't been logged yet
            if self.tracked_objects[obj_id]['count'] >= self.min_frames and obj_id not in self.logged_objects:
                self._log_attendance(class_name, current_time, obj_id)

        # Clean up objects not seen in this frame (but keep them for a few frames)
        objects_to_remove = []
        for obj_id, obj_data in self.tracked_objects.items():
            if obj_id not in current_frame_objects:
                # Only remove if not seen for more than 3 frames (reduce count)
                obj_data['count'] = max(0, obj_data['count'] - 1)
                if obj_data['count'] == 0:
                    objects_to_remove.append(obj_id)

        for obj_id in objects_to_remove:
            del self.tracked_objects[obj_id]
            print(f"[Debug] Removed object: {obj_id}")

        return self.attendance_log

    def _log_attendance(self, name: str, timestamp: str, obj_id: str):
        """Log an attendance record to memory and CSV."""
        record = {'name': name, 'timestamp': timestamp}
        self.attendance_log.append(record)
        self.logged_objects.add(obj_id)  # Mark this object as logged

        with open(self.csv_path, 'a', newline='') as csvfile:
            writer = csv.DictWriter(csvfile, fieldnames=['name', 'timestamp'])
            writer.writerow(record)

        print(f"[Attendance] Logged: {name} at {timestamp}")

    def get_attendance_log(self) -> List[Dict]:
        """Return the current attendance log."""
        return self.attendance_log

    def export_to_csv(self) -> str:
        """Export the current attendance log to the CSV file and return the file path."""
        with open(self.csv_path, 'w', newline='') as csvfile:
            writer = csv.DictWriter(csvfile, fieldnames=['name', 'timestamp'])
            writer.writeheader()
            writer.writerows(self.attendance_log)
        return self.csv_path