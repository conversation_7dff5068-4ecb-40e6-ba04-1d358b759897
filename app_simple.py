#!/usr/bin/env python3
"""
Simplified app that runs only the FastAPI server with built-in attendance tracking.
This bypasses Gradio SSL issues while providing full functionality.
"""

import time
import webbrowser
from src.video_streaming import WebcamStreamer

def run_fastapi_only():
    """Run only the FastAPI server with built-in attendance tracking."""
    print("Starting YOLOv8 Webcam Stream with Attendance Tracking...")
    print("=" * 60)
    
    try:
        # Initialize the webcam streamer with attendance tracking
        streamer = WebcamStreamer(
            host="127.0.0.1",
            port=8000,
            model_path='models/yolov8n.pt'
        )
        
        # Start the FastAPI server
        public_url, local_url = streamer.run()
        
        print(f"✅ FastAPI Server started successfully!")
        print(f"🌐 Local URL: {local_url}")
        if public_url != local_url:
            print(f"🌍 Public URL: {public_url}")
        
        print("\n📋 Features available:")
        print("  • Real-time object detection with YOLOv8")
        print("  • Automatic attendance tracking")
        print("  • Live attendance table (updates every 5 seconds)")
        print("  • CSV export functionality")
        print("  • Manual refresh controls")
        
        print(f"\n🔗 API Endpoints:")
        print(f"  • Main interface: {local_url}")
        print(f"  • Attendance data: {local_url}/api/attendance")
        print(f"  • Download CSV: {local_url}/api/attendance/csv")
        
        print("\n⚙️  Attendance Settings:")
        print("  • Minimum frames for logging: 30 frames")
        print("  • CSV file: attendance_log.csv")
        print("  • Auto-refresh: Every 5 seconds")
        
        # Wait a moment for server to fully start
        time.sleep(3)
        
        # Open the interface in the default browser
        print(f"\n🚀 Opening interface in your default browser...")
        webbrowser.open(local_url)
        
        print(f"\n📝 Instructions:")
        print("  1. Allow camera access when prompted")
        print("  2. Click 'Start Stream' to begin detection")
        print("  3. Objects detected for 30+ frames will appear in attendance log")
        print("  4. Use 'Refresh' button or wait for auto-refresh")
        print("  5. Press Ctrl+C here to stop the server")
        
        print(f"\n🔄 Server is running... Press Ctrl+C to stop")
        print("=" * 60)
        
        # Keep the main thread alive
        try:
            while True:
                time.sleep(1)
        except KeyboardInterrupt:
            print(f"\n\n🛑 Shutting down server...")
            print("Thank you for using the attendance tracking system!")
            
    except FileNotFoundError as e:
        print(f"❌ Error: YOLO model file not found!")
        print(f"Please ensure 'models/yolov8n.pt' exists in the project directory.")
        print(f"Error details: {e}")
        
    except Exception as e:
        print(f"❌ Error starting the application: {e}")
        print(f"Please check the error details above and try again.")

if __name__ == "__main__":
    run_fastapi_only()
