#!/usr/bin/env python3
"""
Test script to verify the attendance tracking app works correctly.
"""

import os
import sys

def check_files():
    """Check if all required files exist."""
    required_files = [
        'models/yolov8n.pt',
        'src/video_streaming.py',
        'src/yolo_processor.py',
        'src/attendance_tracker.py',
        'src/app.py',
        'app_simple.py'
    ]
    
    missing_files = []
    for file_path in required_files:
        if not os.path.exists(file_path):
            missing_files.append(file_path)
    
    if missing_files:
        print("❌ Missing required files:")
        for file_path in missing_files:
            print(f"  - {file_path}")
        return False
    else:
        print("✅ All required files found")
        return True

def test_imports():
    """Test if all modules can be imported."""
    try:
        sys.path.append('src')
        from src.video_streaming import WebcamStreamer
        from src.yolo_processor import YOLOProcessor
        from src.attendance_tracker import AttendanceTracker
        print("✅ All modules imported successfully")
        return True
    except ImportError as e:
        print(f"❌ Import error: {e}")
        return False

def main():
    print("🧪 Testing Attendance Tracking App")
    print("=" * 50)
    
    # Check files
    if not check_files():
        print("\n❌ File check failed. Please ensure all files are in place.")
        return
    
    # Test imports
    if not test_imports():
        print("\n❌ Import test failed. Please check dependencies.")
        return
    
    print("\n✅ All tests passed!")
    print("\n🚀 You can now run the app using:")
    print("   python app_simple.py")
    print("\n📋 Features:")
    print("   • Object detection with YOLOv8")
    print("   • Attendance tracking (each class recorded once)")
    print("   • Gradio interface with attendance table")
    print("   • CSV export functionality")

if __name__ == "__main__":
    main()
