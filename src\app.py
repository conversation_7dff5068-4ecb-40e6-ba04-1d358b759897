
# #venv\Scripts\activate
# import gradio as gr

# # URL của ứng dụng FastAPI webcam độc lập
# FASTAPI_WEBCAM_APP_URL = 'http://127.0.0.1:8000/' #"http://127.0.0.1:8000/" 
# # Đ<PERSON><PERSON> bảo đâ<PERSON> là IP mà FastAPI đang chạy (ví dụ: ************* nếu chạy trên máy khác)

# # Tạo giao diện Gradio
# with gr.Blocks(theme=gr.themes.Soft(), title="YOLOv8 Webcam Stream (Embedded)") as demo:
#     gr.Markdown(
#         """
#         # Real-Time Object Detection with Client Webcam
#         """
#     )
    
#     gr.HTML(f"""
#         <iframe src="{FASTAPI_WEBCAM_APP_URL}" 
#                 width="100%" 
#                 height="800px" 
#                 frameborder="0" 
#                 allow="camera; microphone"
#                 style="border: 2px solid #ddd; border-radius: 8px;">
#         </iframe>
#         <p style="text-align: center; margin-top: 10px;">
#             Nếu iframe không hiển thị, hãy truy cập trực tiếp: 
#             <a href="{FASTAPI_WEBCAM_APP_URL}" target="_blank">{FASTAPI_WEBCAM_APP_URL}</a>
#         </p>
#     """)



# demo.launch(
#     server_name="127.0.0.1",
#     server_port=7860,
#     share=True,
#     debug=True
# )
# 


import threading
import time
import webbrowser
from video_streaming import WebcamStreamer
import csv

def get_attendance_data(fastapi_url):
    """Fetch attendance data from the FastAPI server."""
    try:
        import requests
        response = requests.get(f"{fastapi_url}/api/attendance", timeout=5)
        if response.status_code == 200:
            data = response.json()
            return data.get('attendance', [])
        else:
            return []
    except Exception as e:
        print(f"Error fetching attendance data: {e}")
        return []

def run_gradio(fastapi_url):
    """Run Gradio interface with error handling for SSL issues."""
    try:
        import gradio as gr
        import requests
        print("✅ Gradio imported successfully")
    except Exception as e:
        print(f"❌ Gradio import failed: {e}")
        print("🔄 Falling back to FastAPI-only mode...")
        print(f"🌐 Please open your browser and go to: {fastapi_url}")

        # Open browser automatically
        time.sleep(2)
        webbrowser.open(fastapi_url)

        # Keep the main thread alive
        try:
            print("Press Ctrl+C to stop the server...")
            while True:
                time.sleep(1)
        except KeyboardInterrupt:
            print("\nShutting down...")
        return

    try:
        with gr.Blocks(theme=gr.themes.Soft(), title="YOLOv8 Webcam Stream with Attendance") as demo:
            gr.Markdown("# Real-Time Object Detection with Attendance Tracking")

            with gr.Row():
                with gr.Column(scale=2):
                    gr.HTML(f"""
                        <iframe src="{fastapi_url}"
                                width="100%"
                                height="600px"
                                frameborder="0"
                                allow="camera; microphone"
                                style="border: 2px solid #ddd; border-radius: 8px;">
                        </iframe>
                        <p style="text-align: center; margin-top: 10px;">
                            If iframe does not display, visit directly:
                            <a href="{fastapi_url}" target="_blank">{fastapi_url}</a>
                        </p>
                    """)

                with gr.Column(scale=1):
                    gr.Markdown("## Attendance Log")
                    attendance_df = gr.Dataframe(
                        headers=["Name", "Timestamp"],
                        datatype=["str", "str"],
                        interactive=False,
                        elem_id="attendance_table"
                    )

                    refresh_btn = gr.Button("Refresh Attendance", variant="primary")
                    download_btn = gr.Button("Download CSV", variant="secondary")
                    download_file = gr.File(label="Downloaded CSV", visible=False)

                    def refresh_attendance():
                        data = get_attendance_data(fastapi_url)
                        if data:
                            return [[record['name'], record['timestamp']] for record in data]
                        else:
                            return []

                    def download_csv():
                        try:
                            response = requests.get(f"{fastapi_url}/api/attendance/csv")
                            if response.status_code == 200:
                                with open("attendance_log.csv", "wb") as f:
                                    f.write(response.content)
                                print("CSV file downloaded successfully to attendance_log.csv")
                                return "attendance_log.csv"
                            else:
                                print("Failed to download CSV file")
                                return None
                        except Exception as e:
                            print(f"Error downloading CSV: {e}")
                            return None

                    refresh_btn.click(refresh_attendance, outputs=attendance_df)
                    download_btn.click(download_csv, outputs=download_file)

                    # Auto-refresh every 10 seconds (more conservative)
                    try:
                        demo.load(refresh_attendance, outputs=attendance_df, every=10)
                    except:
                        # If auto-refresh fails, just use manual refresh
                        print("Auto-refresh not supported, use manual refresh button")

            demo.launch(server_name="127.0.0.1", server_port=7860, share=False, debug=False)

    except Exception as e:
        print(f"❌ Gradio failed to start: {e}")
        print("🔄 Falling back to FastAPI-only mode...")
        print(f"🌐 Please open your browser and go to: {fastapi_url}")

        # Open browser automatically
        time.sleep(2)
        webbrowser.open(fastapi_url)

        # Keep the main thread alive
        try:
            print("Press Ctrl+C to stop the server...")
            while True:
                time.sleep(1)
        except KeyboardInterrupt:
            print("\nShutting down...")
def get_attendance_log():
    try:
        with open('attendance_log.csv', 'r') as csvfile:
            reader = csv.DictReader(csvfile)
            attendance_log = [row for row in reader]
            return attendance_log
    except FileNotFoundError:
        return []
def run_both():
    streamer = WebcamStreamer(model_path='models/yolov8n.pt')
    public_url, local_url = streamer.run()
    gradio_thread = threading.Thread(target=run_gradio, args=(local_url,))
    gradio_thread.start()
    gradio_thread.join()

if __name__ == "__main__":
    run_both()